from app import create_app, db
from app.models import User, Conversation, ChatMessageNode, SystemSetting # Ensure all models are known for Flask-Migrate

app = create_app()

# This makes db, User etc. available in 'flask shell'
@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User, 'Conversation': Conversation, 'ChatMessageNode': ChatMessageNode, 'SystemSetting': SystemSetting}

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)