# Flask specific
SECRET_KEY='your_very_secret_random_string_here' # Generate a strong random key

# Database
DATABASE_URL='sqlite:///../instance/app.db' # Path relative to app/__init__.py

# LLM Configuration
OPENAI_API_KEY='sk-your_openai_api_key_here' # Your actual OpenAI API key
OPENAI_API_BASE='https://api.openai.com/v1' # Default OpenAI endpoint
# If using a local LLM (e.g., LM Studio, Ollama with OpenAI compatibility):
# OPENAI_API_BASE='http://localhost:1234/v1' # Example for LM Studio
# OPENAI_API_KEY='not-needed-for-local' # Or whatever your local server expects

# Admin user (created on first run if not exists)
ADMIN_EMAIL='<EMAIL>'
ADMIN_PASSWORD='strongpassword123'