from flask import request, jsonify, current_app
from flask_login import login_required, current_user
from app import db # 确保 db 实例已正确初始化
from app.models import Conversation, ChatMessageNode, SystemSetting # 导入新模型
from app.chat import bp # 假设 bp = Blueprint('chat', __name__, url_prefix='/api')
from app.services.llm_service import get_llm_completion # LLM 服务接口
import uuid # 用于生成 node_id (如果模型默认值未覆盖所有场景)
from datetime import datetime # 用于更新时间戳

# 辅助函数：为 LLM 构建历史记录
def build_llm_history(chat_id: int, current_turn_user_node: ChatMessageNode):
    """
    从 chat_session.entry_node_id 开始，沿着 active_next_node_id
    遍历到 current_turn_user_node（包含它），构建 LLM 需要的 {role, content} 列表。
    """
    conversation = Conversation.query.get_or_404(chat_id)
    if not conversation.entry_node_id:
        if current_turn_user_node.role == 'user': # 确保当前节点是起始节点
             return [{"role": "user", "content": current_turn_user_node.content}]
        else: # 不应该发生，但作为防御
            return []

    history_for_llm = []

    # Fetch system prompt
    system_prompt_setting = SystemSetting.query.filter_by(key='system_prompt').first()
    system_prompt_content = system_prompt_setting.value if system_prompt_setting else "You are a helpful assistant."
    history_for_llm.append({"role": "system", "content": system_prompt_content})

    # 获取所有节点以在内存中构建路径，避免多次数据库查询
    all_nodes_dict = {node.node_id: node for node in conversation.message_nodes.all()}

    # 简化版：假设我们总是从头构建到 current_turn_user_node
    # 这是一个简化的实现，实际应用中需要更鲁棒的路径遍历算法
    # 按时间戳排序获取相关节点（这不是完全正确的分支处理，但作为初始实现）
    relevant_nodes = ChatMessageNode.query.filter_by(conversation_id=chat_id)\
                                       .filter(ChatMessageNode.timestamp <= current_turn_user_node.timestamp)\
                                       .order_by(ChatMessageNode.timestamp.asc()).all()

    for msg_node in relevant_nodes:
        # 避免重复添加当前用户消息
        if msg_node.node_id == current_turn_user_node.node_id:
            if not any(h['role'] == 'user' and h['content'] == current_turn_user_node.content for h in history_for_llm):
                 history_for_llm.append({'role': msg_node.role, 'content': msg_node.content})
        else:
            history_for_llm.append({'role': msg_node.role, 'content': msg_node.content})

    # 确保最后一条消息是当前用户的内容
    final_user_message = {'role': 'user', 'content': current_turn_user_node.content}
    if history_for_llm[-1] != final_user_message:
        # 移除可能由于时间戳顺序导致的最后一条用户消息并替换
        if history_for_llm[-1]['role'] == 'user':
            history_for_llm.pop()
        history_for_llm.append(final_user_message)

    return history_for_llm


@bp.route('/chats', methods=['POST'])
@login_required
def create_chat_session():
    data = request.get_json()
    title = data.get('title', 'New Chat')
    conversation = Conversation(user_id=current_user.id, title=title, entry_node_id=None)
    db.session.add(conversation)
    db.session.commit()
    # Convert conversation.id to string for chat_id
    response_data = conversation.to_dict_full()
    response_data['id'] = str(conversation.id) # Ensure ID is string
    return jsonify(response_data), 201

@bp.route('/chats', methods=['GET'])
@login_required
def get_chat_sessions_list():
    conversations = Conversation.query.filter_by(user_id=current_user.id).order_by(Conversation.updated_at.desc()).all()
    return jsonify([c.to_dict_metadata() for c in conversations]), 200

@bp.route('/chats/<string:chat_id_str>', methods=['GET']) # chat_id is now string (from Conversation.id as int)
@login_required
def get_chat_session_details(chat_id_str):
    try:
        chat_id = int(chat_id_str) # Convert back to int for DB query
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    return jsonify(conversation.to_dict_full()), 200

@bp.route('/chats/<string:chat_id_str>', methods=['DELETE'])
@login_required
def delete_chat_session(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()

    # Prevent deleting the last conversation (if this logic is still desired)
    # user_conv_count = Conversation.query.filter_by(user_id=current_user.id).count()
    # if user_conv_count <= 1:
    #    return jsonify({'message': 'Cannot delete the last conversation.'}), 400

    db.session.delete(conversation) # Cascading delete should handle ChatMessageNodes
    db.session.commit()
    return jsonify({'message': 'Conversation deleted'}), 200

@bp.route('/chats/<string:chat_id_str>/rename', methods=['PUT'])
@login_required
def rename_chat_session(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    data = request.get_json()
    new_title = data.get('title')
    if not new_title or len(new_title.strip()) == 0 or len(new_title) > 150 :
        return jsonify({'message': 'Valid title is required (1-150 chars)'}), 400

    conversation.title = new_title
    conversation.updated_at = datetime.utcnow()
    db.session.commit()
    return jsonify({'message': 'Conversation title updated', 'id': chat_id_str, 'new_title': new_title}), 200

@bp.route('/chats/<string:chat_id_str>/message_turns', methods=['POST'])
@login_required
def post_message_turn(chat_id_str):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()
    data = request.get_json()

    origin_node_id = data.get('origin_node_id') # Can be null
    action_type = data.get('action_type')
    user_content = data.get('user_content') # Required for 'new_message' and 'edit_user_message'

    if not action_type or action_type not in ['new_message', 'edit_user_message', 'regenerate_assistant_message']:
        return jsonify({'message': "Invalid action_type"}), 400
    if action_type in ['new_message', 'edit_user_message'] and not user_content:
        return jsonify({'message': "user_content is required for this action_type"}), 400

    new_user_node_data = None
    new_assistant_node_data = None
    updated_origin_node_links_data = None

    # --- Logic for action_type ---
    # Common: create user node (if applicable), link it, call LLM, create assistant node, link it.

    # Fetch origin_node if provided
    origin_node = None
    if origin_node_id:
        origin_node = ChatMessageNode.query.filter_by(node_id=origin_node_id, conversation_id=chat_id).first()
        if not origin_node:
            return jsonify({'message': f"Origin node {origin_node_id} not found in this chat."}), 404

    # 1. Handle User Message Node creation and linking
    user_node_for_llm_input = None

    if action_type == 'new_message' or action_type == 'edit_user_message':
        current_user_node = ChatMessageNode(
            conversation_id=chat_id,
            role='user',
            content=user_content,
            message_metadata=data.get('metadata', {}) # Allow frontend to pass metadata
        )
        db.session.add(current_user_node)
        # db.session.flush() # Get node_id assigned if not set by default in constructor

        if origin_node: # This new user message follows origin_node
            # Add current_user_node.node_id to origin_node.next_node_ids
            # Ensure next_node_ids is treated as a list
            if origin_node.next_node_ids is None:
                origin_node.next_node_ids = []

            # Make a copy to modify if it's immutable from DB JSON, or ensure it's mutable
            temp_next_ids = list(origin_node.next_node_ids)
            if current_user_node.node_id not in temp_next_ids:
                 temp_next_ids.append(current_user_node.node_id)
            origin_node.next_node_ids = temp_next_ids

            origin_node.active_next_node_id = current_user_node.node_id
            db.session.add(origin_node)
            updated_origin_node_links_data = origin_node.to_dict() # Or just relevant fields
        else: # No origin_node, this is the first user message in the chat (or a detached branch root - handle carefully)
            if not conversation.entry_node_id: # Truly the first message
                conversation.entry_node_id = current_user_node.node_id
            # else: This implies a new root for a branch, which might need special handling
            # for now, assume 'new_message' with null origin_node_id is for empty chats.
            # 'edit_user_message' MUST have an origin_node_id (the predecessor of the edited message).

        user_node_for_llm_input = current_user_node
        new_user_node_data = current_user_node.to_dict()

    elif action_type == 'regenerate_assistant_message':
        if not origin_node or origin_node.role != 'user':
            return jsonify({'message': "For regenerate_assistant_message, origin_node_id must point to a user message."}), 400
        user_node_for_llm_input = origin_node # LLM responds to this existing user message
        # No new_user_node_data in this case, but origin_node's links will be updated later.
        updated_origin_node_links_data = origin_node.to_dict() # Capture state before potential LLM error

    # 2. Call LLM Service
    if not user_node_for_llm_input: # Should not happen if logic above is correct
        db.session.rollback()
        return jsonify({'message': "Internal error: user context for LLM not established."}), 500

    # IMPORTANT: build_llm_history needs robust implementation based on traversal
    # from conversation.entry_node_id through active_next_node_id links
    # until the parent of user_node_for_llm_input, then add user_node_for_llm_input.
    llm_history = build_llm_history(chat_id, user_node_for_llm_input)

    assistant_content = get_llm_completion(llm_history)

    if assistant_content is None or ("Error:" in assistant_content and isinstance(assistant_content, str)): # Crude error check
        db.session.rollback() # Rollback user message and origin_node changes if LLM fails
        return jsonify({'message': 'Error getting response from LLM', 'details': assistant_content}), 500

    # 3. Create Assistant Message Node and link it
    current_assistant_node = ChatMessageNode(
        conversation_id=chat_id,
        role='assistant',
        content=assistant_content,
        message_metadata={} # Add LLM model info if available
    )
    db.session.add(current_assistant_node)
    # db.session.flush()

    # Link from the user node (either newly created or the origin_node for regeneration) to the new assistant node
    node_before_assistant = user_node_for_llm_input # This is current_user_node for new/edit, or origin_node for regenerate

    if node_before_assistant.next_node_ids is None:
        node_before_assistant.next_node_ids = []

    temp_assistant_parent_next_ids = list(node_before_assistant.next_node_ids)
    if current_assistant_node.node_id not in temp_assistant_parent_next_ids:
        temp_assistant_parent_next_ids.append(current_assistant_node.node_id)
    node_before_assistant.next_node_ids = temp_assistant_parent_next_ids

    node_before_assistant.active_next_node_id = current_assistant_node.node_id
    db.session.add(node_before_assistant)

    # If user_node_for_llm_input was origin_node (regenerate case), then updated_origin_node_links_data needs to reflect these new links
    if action_type == 'regenerate_assistant_message':
         updated_origin_node_links_data = node_before_assistant.to_dict()
    elif new_user_node_data and node_before_assistant.node_id == new_user_node_data['node_id']: # new/edit case, update new_user_node_data
        new_user_node_data = node_before_assistant.to_dict()


    new_assistant_node_data = current_assistant_node.to_dict()

    # Update conversation timestamp
    conversation.updated_at = datetime.utcnow()
    db.session.add(conversation)
    db.session.commit()

    return jsonify({
        'new_user_node': new_user_node_data, # Null if regenerate
        'new_assistant_node': new_assistant_node_data,
        'updated_origin_node_links': updated_origin_node_links_data, # Null if new_message was first in chat
        'chat_session_meta_update': {
            'id': chat_id_str,
            'updated_at': conversation.updated_at.isoformat() + 'Z',
            'entry_node_id': conversation.entry_node_id
        }
    }), 201


@bp.route('/chats/<string:chat_id_str>/nodes/<string:node_id>/set_active_branch', methods=['PUT'])
@login_required
def set_active_branch(chat_id_str, node_id):
    try:
        chat_id = int(chat_id_str)
    except ValueError:
        return jsonify({'message': 'Invalid chat_id format'}), 400

    conversation = Conversation.query.filter_by(id=chat_id, user_id=current_user.id).first_or_404()

    node_to_update = ChatMessageNode.query.filter_by(node_id=node_id, conversation_id=chat_id).first()
    if not node_to_update:
        return jsonify({'message': f"Node {node_id} not found in this chat."}), 404

    data = request.get_json()
    active_next_node_id_from_request = data.get('active_next_node_id')

    if not active_next_node_id_from_request:
        return jsonify({'message': "active_next_node_id is required in payload."}), 400

    if node_to_update.next_node_ids is None or active_next_node_id_from_request not in node_to_update.next_node_ids:
        return jsonify({'message': f"{active_next_node_id_from_request} is not a valid next node for {node_id}."}), 400

    node_to_update.active_next_node_id = active_next_node_id_from_request
    conversation.updated_at = datetime.utcnow() # Also update conversation timestamp

    db.session.add(node_to_update)
    db.session.add(conversation)
    db.session.commit()

    return jsonify({
        'message': 'Active branch switched',
        'updated_node_id': node_id,
        'new_active_next_node_id': active_next_node_id_from_request
    }), 200