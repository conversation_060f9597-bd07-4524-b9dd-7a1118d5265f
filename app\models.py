from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import uuid # 用于生成 node_id

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=True) # 邮箱可以为空，如果只用手机注册
    phone_number = db.Column(db.String(20), unique=True, nullable=True) # 手机号可以为空，如果只用邮箱注册
    password_hash = db.Column(db.String(256))
    is_admin = db.Column(db.<PERSON>, default=False)
    email_verified = db.Column(db.<PERSON>, default=False)
    phone_verified = db.Column(db.<PERSON>, default=False)
    conversations = db.relationship('Conversation', backref='user', lazy='dynamic', cascade="all, delete-orphan")

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        if not self.password_hash: # 用户可能通过手机验证码注册，未设置密码
            return False
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.email or self.phone_number}>'

class Conversation(db.Model): # 代表前端的 ChatSession
    id = db.Column(db.Integer, primary_key=True) # 对应前端 ChatSession.chat_id (类型转换处理)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(150), default='New Chat')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 新增字段：对话树的入口节点 ID
    entry_node_id = db.Column(db.String(36), nullable=True) # 存储 ChatMessageNode.node_id (UUID)

    # 关联所有的消息节点
    # cascade="all, delete-orphan" 确保删除 Conversation 时，其下的 ChatMessageNode 也被删除
    message_nodes = db.relationship('ChatMessageNode', backref='conversation', lazy='dynamic', cascade="all, delete-orphan")

    def __repr__(self):
        return f'<Conversation {self.id} - {self.title}>'

    def to_dict_metadata(self): # 用于列表，不含 message_nodes
        return {
            'id': str(self.id), # 确保 chat_id 为 string, 与前端对应
            'title': self.title,
            'created_at': self.created_at.isoformat() + 'Z',
            'updated_at': self.updated_at.isoformat() + 'Z',
            'entry_node_id': self.entry_node_id
        }

    def to_dict_full(self): # 用于获取单个会话详情
        # message_nodes 需要 dynamic-loaded query and then conversion to dict
        nodes = [node.to_dict() for node in self.message_nodes.all()]
        return {
            'id': str(self.id),
            'title': self.title,
            'created_at': self.created_at.isoformat() + 'Z',
            'updated_at': self.updated_at.isoformat() + 'Z',
            'entry_node_id': self.entry_node_id,
            'message_nodes': nodes
        }

class ChatMessageNode(db.Model): # 替换旧的 Message 模型
    # 使用 UUID 作为 node_id，更适合分布式/非序列化ID场景
    node_id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = db.Column(db.Integer, db.ForeignKey('conversation.id'), nullable=False) # 对应 chat_id

    role = db.Column(db.String(20), nullable=False)  # 'user' or 'assistant'
    content = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # (JSON 类型) 可选的元数据
    message_metadata = db.Column(db.JSON, nullable=True)

    # (JSON 类型) 存储一个包含后续可能消息节点的 node_id 的字符串数组
    # SQLAlchemy 通常使用 db.Text 或特定数据库的 JSON 类型来存储 JSON
    # 对于 PostgreSQL 可以用 db.JSONB，对于 SQLite/MySQL 可以用 db.Text 然后手动序列化/反序列化或 db.JSON
    next_node_ids = db.Column(db.JSON, default=list, nullable=False) # e.g., ["node_id_1", "node_id_2"]

    # (字符串类型) 指向 next_node_ids 中的一个，表示当前激活的对话分支
    active_next_node_id = db.Column(db.String(36), nullable=True)

    def __repr__(self):
        return f'<ChatMessageNode {self.node_id} from {self.role}>'

    def to_dict(self):
        return {
            'node_id': self.node_id,
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat() + 'Z', # 保持与前端一致的ISO格式
            'metadata': self.message_metadata or {},
            'next_node_ids': self.next_node_ids or [],
            'active_next_node_id': self.active_next_node_id
        }

class SystemSetting(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.String(500)) # Store values as strings, parse as needed

    def __repr__(self):
        return f'<SystemSetting {self.key}: {self.value}>'